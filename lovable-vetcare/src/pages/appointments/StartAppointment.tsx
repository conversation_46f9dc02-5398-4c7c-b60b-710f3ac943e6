import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { ArrowLeft, Save, Plus, Trash2, Wand2, FileText, Stethoscope, Users, ExternalLink, Brain, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { getAppointmentById } from '@/services/appointments';
import { getServices, createService } from '@/services/services';
import { api } from '@/services/api';
import { cn } from '@/lib/utils';
import TaskAssignment from '@/components/appointments/TaskAssignment';
import AISuggestions from '@/components/appointments/AISuggestions';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';

interface Service {
  serviceId: number;
  serviceName: string;
  description: string;
  category: string;
  defaultPrice: number;
  currency: string;
  estimatedDuration: number;
}

interface AppointmentService {
  serviceId: number;
  serviceName: string;
  price: number;
  currency: string;
  notes?: string;
  assignedStaffId?: number;
  assignedStaffName?: string;
  isExternalReferral?: boolean;
  externalClinic?: {
    name: string;
    contact: string;
    address?: string;
  };
}

interface AppointmentNote {
  category: string;
  content: string;
  isAIGenerated: boolean;
}

const StartAppointment: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [selectedServices, setSelectedServices] = useState<AppointmentService[]>([]);
  const [appointmentNotes, setAppointmentNotes] = useState<AppointmentNote[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [newServiceData, setNewServiceData] = useState({
    serviceName: '',
    description: '',
    defaultPrice: 0,
    estimatedDuration: 30
  });
  const [showCreateServiceModal, setShowCreateServiceModal] = useState(false);
  const [isGeneratingNotes, setIsGeneratingNotes] = useState(false);
  const [appointmentTypes, setAppointmentTypes] = useState<string[]>([]);
  const [showStaffAssignModal, setShowStaffAssignModal] = useState(false);
  const [selectedServiceForAssignment, setSelectedServiceForAssignment] = useState<number | null>(null);
  const [showExternalReferralModal, setShowExternalReferralModal] = useState(false);
  const [externalClinicData, setExternalClinicData] = useState({
    name: '',
    contact: '',
    address: ''
  });
  const [showCompletionOptions, setShowCompletionOptions] = useState(false);
  const [activeTab, setActiveTab] = useState('assignment');
  const [workflowStep, setWorkflowStep] = useState<'assignment' | 'services' | 'ai-suggestions' | 'completion'>('assignment');

  // Fetch appointment data
  const { data: appointmentResponse, isLoading } = useQuery({
    queryKey: ['appointment', id],
    queryFn: () => getAppointmentById(parseInt(id!)),
    enabled: !!id
  });

  // Fetch services
  const { data: servicesResponse, refetch: refetchServices } = useQuery({
    queryKey: ['services', selectedCategory],
    queryFn: () => getServices({
      category: selectedCategory || undefined,
      limit: 100
    })
  });

  const appointment = appointmentResponse?.data;
  const services = servicesResponse?.data?.services || [];

  // Fetch clinic staff for assignments
  const { data: staffResponse } = useQuery({
    queryKey: ['clinic-staff', appointment?.clinicId],
    queryFn: async () => {
      if (!appointment?.clinicId) return { data: { data: [] } };
      return await api.get(`/staff?clinicId=${appointment.clinicId}&status=1&limit=100`);
    },
    enabled: !!appointment?.clinicId
  });

  const clinicStaff = staffResponse?.data?.data || [];

  // Extract appointment types and set initial category when appointment loads
  useEffect(() => {
    if (appointment?.appointmentTypes) {
      const types = appointment.appointmentTypes.map((at: any) => at.appointmentTypeId?.toString());
      setAppointmentTypes(types);

      // Set initial category based on first appointment type
      if (appointment.appointmentTypes.length > 0) {
        const firstType = appointment.appointmentTypes[0];
        // Map appointment type to service category
        const typeToCategory: { [key: string]: string } = {
          'Vaccination': 'vaccination',
          'Consultation': 'consultation',
          'Laboratory': 'laboratory',
          'Surgery': 'surgery',
          'Grooming': 'grooming',
          'Dental': 'dental',
          'Emergency': 'emergency',
          'Wellness Check': 'wellness',
          'Physical Therapy': 'therapy',
          'X-ray/Imaging': 'imaging',
          'Behavioral Consultation': 'behavioral',
          'Nutrition Consultation': 'nutrition'
        };

        const category = typeToCategory[firstType.name] || 'consultation';
        setSelectedCategory(category);
      }
    }
  }, [appointment]);

  // Update appointment status to in_progress when component mounts
  useEffect(() => {
    if (appointment && appointment.status === 'scheduled') {
      const updateStatus = async () => {
        try {
          await api.put(`/appointments/${id}/progress`, {
            status: 'in_progress',
            clinicId: appointment.clinicId
          });
          queryClient.invalidateQueries({ queryKey: ['appointment', id] });
        } catch (error) {
          console.error('Failed to update appointment status:', error);
        }
      };
      updateStatus();
    }
  }, [appointment, id, queryClient]);

  // Service categories based on appointment types
  const serviceCategories = [
    'consultation', 'vaccination', 'laboratory', 'surgery', 'grooming',
    'dental', 'emergency', 'medication', 'therapy', 'imaging', 'other'
  ];

  // Create service mutation
  const createServiceMutation = useMutation({
    mutationFn: createService,
    onSuccess: (response) => {
      if (response.success) {
        toast({
          title: "Success",
          description: "Service created successfully",
        });
        setShowCreateServiceModal(false);
        setNewServiceData({
          serviceName: '',
          description: '',
          defaultPrice: 0,
          estimatedDuration: 30
        });
        refetchServices();
      }
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create service",
        variant: "destructive",
      });
    }
  });

  const handleAddService = (service: Service) => {
    const existingService = selectedServices.find(s => s.serviceId === service.serviceId);
    if (existingService) {
      toast({
        title: "Service already added",
        description: "This service is already in the appointment",
        variant: "destructive",
      });
      return;
    }

    const appointmentService: AppointmentService = {
      serviceId: service.serviceId,
      serviceName: service.serviceName,
      price: service.defaultPrice,
      currency: service.currency,
      notes: ''
    };

    setSelectedServices([...selectedServices, appointmentService]);
  };

  const handleRemoveService = (serviceId: number) => {
    setSelectedServices(selectedServices.filter(s => s.serviceId !== serviceId));
  };

  const handleUpdateServiceNotes = (serviceId: number, notes: string) => {
    setSelectedServices(selectedServices.map(s =>
      s.serviceId === serviceId ? { ...s, notes } : s
    ));
  };

  const handleCreateService = () => {
    if (!newServiceData.serviceName || !selectedCategory) {
      toast({
        title: "Error",
        description: "Please fill in service name and select a category first",
        variant: "destructive",
      });
      return;
    }

    createServiceMutation.mutate({
      ...newServiceData,
      category: selectedCategory, // Use the selected category
      currency: 'KES',
      isActive: true,
      isCustom: true,
      clinicId: appointment?.clinicId || 1019, // Use appointment's clinicId
      createdBy: 1020 // This should come from auth context
    });
  };



  const handleAssignStaff = (serviceId: number, staffId: number, staffName: string) => {
    setSelectedServices(selectedServices.map(s =>
      s.serviceId === serviceId
        ? { ...s, assignedStaffId: staffId, assignedStaffName: staffName, isExternalReferral: false }
        : s
    ));
    setShowStaffAssignModal(false);
    setSelectedServiceForAssignment(null);
  };

  const handleExternalReferral = (serviceId: number) => {
    if (!externalClinicData.name || !externalClinicData.contact) {
      toast({
        title: "Error",
        description: "Please fill in clinic name and contact",
        variant: "destructive",
      });
      return;
    }

    setSelectedServices(selectedServices.map(s =>
      s.serviceId === serviceId
        ? {
            ...s,
            isExternalReferral: true,
            externalClinic: { ...externalClinicData },
            assignedStaffId: undefined,
            assignedStaffName: undefined
          }
        : s
    ));
    setShowExternalReferralModal(false);
    setSelectedServiceForAssignment(null);
    setExternalClinicData({ name: '', contact: '', address: '' });
  };

  const generateAINotes = async (category: string, aiOption?: string) => {
    setIsGeneratingNotes(true);
    try {
      // Simulate AI note generation based on services, category, and AI option
      const categoryServices = selectedServices.filter(s =>
        services.find(service => service.serviceId === s.serviceId)?.category === category
      );

      let aiNotes = '';

      // Generate notes based on AI option
      switch (aiOption) {
        case 'Standard Assessment':
          aiNotes = `Standard ${category} assessment completed.\n\nFindings:\n- Patient alert and responsive\n- No immediate concerns noted\n- Vital signs within normal range\n\nRecommendations:\n- Continue current care plan\n- Monitor for any changes`;
          break;
        case 'Detailed Examination':
          aiNotes = `Comprehensive ${category} examination performed.\n\nDetailed Findings:\n- Thorough physical examination completed\n- All systems evaluated\n- Diagnostic tests reviewed\n\nClinical Assessment:\n- Patient condition stable\n- No abnormalities detected\n- Treatment plan effective`;
          break;
        case 'Treatment Summary':
          aiNotes = `${category} treatment summary:\n\nTreatments Administered:\n`;
          categoryServices.forEach(service => {
            aiNotes += `- ${service.serviceName}: Successfully completed\n`;
          });
          aiNotes += `\nPatient Response:\n- Tolerated treatment well\n- No adverse reactions\n- Expected recovery timeline`;
          break;
        case 'Follow-up Instructions':
          aiNotes = `${category} follow-up instructions:\n\n1. Monitor patient for 24-48 hours\n2. Continue prescribed medications as directed\n3. Return if symptoms worsen\n4. Schedule follow-up appointment in 1-2 weeks\n\nEmergency Contact:\nCall clinic immediately if any concerning symptoms develop.`;
          break;
        case 'Medication Notes':
          aiNotes = `${category} medication notes:\n\nPrescribed Medications:\n- Review current medications\n- Dosage adjustments made as needed\n- Patient education provided\n\nCompliance:\n- Patient understands instructions\n- No known drug allergies\n- Monitoring plan established`;
          break;
        case 'Behavioral Observations':
          aiNotes = `${category} behavioral observations:\n\nBehavior Assessment:\n- Patient cooperative during examination\n- Normal interaction patterns observed\n- Stress levels manageable\n\nRecommendations:\n- Continue positive reinforcement\n- Environmental enrichment suggested\n- Behavioral monitoring ongoing`;
          break;
        default:
          // Default generation
          if (categoryServices.length > 0) {
            aiNotes = `Services performed in ${category}:\n`;
            categoryServices.forEach(service => {
              aiNotes += `- ${service.serviceName}: Completed successfully\n`;
            });
            aiNotes += `\nPatient responded well to treatment. No adverse reactions observed.`;
          } else {
            aiNotes = `${category.charAt(0).toUpperCase() + category.slice(1)} assessment completed. Patient condition stable.`;
          }
      }

      const newNote: AppointmentNote = {
        category,
        content: aiNotes,
        isAIGenerated: true
      };

      setAppointmentNotes([...appointmentNotes, newNote]);

      toast({
        title: "AI Notes Generated",
        description: `${aiOption || 'Standard'} notes generated for ${category} category`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate AI notes",
        variant: "destructive",
      });
    } finally {
      setIsGeneratingNotes(false);
    }
  };

  const handleAddManualNote = (category: string, content: string) => {
    if (!content.trim()) return;

    const newNote: AppointmentNote = {
      category,
      content: content.trim(),
      isAIGenerated: false
    };

    setAppointmentNotes([...appointmentNotes, newNote]);
  };

  const handleSaveAppointmentProgress = async () => {
    try {
      // Check if all categories have notes
      const categoriesWithNotes = [...new Set(appointmentNotes.map(note => note.category))];
      const allCategoriesComplete = serviceCategories.every(category =>
        categoriesWithNotes.includes(category) || !selectedServices.some(s =>
          services.find(service => service.serviceId === s.serviceId)?.category === category
        )
      );

      // Save services and notes to appointment
      const updateData = {
        services: selectedServices,
        notes: appointmentNotes,
        status: allCategoriesComplete ? 'completed' : 'in_progress',
        clinicId: appointment?.clinicId,
        staffId: appointment?.staffId,
        appointmentId: parseInt(id!)
      };

      await api.put(`/appointments/${id}/progress`, updateData);

      // Invalidate queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['appointment', id] });

      toast({
        title: "Progress Saved",
        description: allCategoriesComplete
          ? "Appointment completed! All categories have notes."
          : "Appointment progress has been saved",
      });

      if (allCategoriesComplete) {
        // Show completion options
        setShowCompletionOptions(true);
      }
    } catch (error) {
      console.error('Save progress error:', error);
      toast({
        title: "Error",
        description: "Failed to save appointment progress",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading appointment...</p>
        </div>
      </div>
    );
  }

  if (!appointment) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600">Appointment not found</p>
          <Button onClick={() => navigate('/appointments')} className="mt-4">
            Back to Appointments
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-4 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/appointments/${id}`)}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Details
          </Button>
          <div>
            <h1 className="text-3xl font-bold">Appointment Workflow</h1>
            <p className="text-gray-600 text-lg">
              {appointment.petName} - {appointment.clientName}
            </p>
            <p className="text-sm text-gray-500">
              {format(new Date(appointment.dateTime), 'PPp')} • Status: {appointment.status}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Badge
            variant={appointment.status === 'in_progress' ? 'default' : 'secondary'}
            className="px-3 py-1"
          >
            {appointment.status === 'in_progress' ? 'In Progress' : appointment.status}
          </Badge>
          <Button onClick={handleSaveAppointmentProgress} className="bg-green-600 hover:bg-green-700">
            <Save className="h-4 w-4 mr-2" />
            Save Progress
          </Button>
        </div>
      </div>

      {/* Progress Indicator */}
      <Card className="mb-6 bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold text-blue-900">Appointment Progress</h3>
              <p className="text-sm text-blue-700">
                {selectedServices.length} services added • {appointmentNotes.length} notes recorded
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-blue-700">
                Categories with notes: {[...new Set(appointmentNotes.map(note => note.category))].length}
              </p>
              <p className="text-xs text-blue-600">
                Add services and document findings for each category
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Workflow Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="assignment" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Task Assignment
          </TabsTrigger>
          <TabsTrigger value="services" className="flex items-center gap-2">
            <Stethoscope className="h-4 w-4" />
            Services & Notes
          </TabsTrigger>
          <TabsTrigger value="ai-suggestions" className="flex items-center gap-2">
            <Brain className="h-4 w-4" />
            AI Suggestions
          </TabsTrigger>
          <TabsTrigger value="completion" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Complete
          </TabsTrigger>
        </TabsList>

        {/* Task Assignment Tab */}
        <TabsContent value="assignment" className="mt-6">
          <TaskAssignment
            appointmentId={parseInt(id!)}
            selectedAppointmentTypes={appointment?.appointmentTypes || []}
            clinicId={appointment?.clinicId}
            onAssignmentComplete={() => setActiveTab('services')}
          />
        </TabsContent>

        {/* Services & Notes Tab */}
        <TabsContent value="services" className="mt-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
            {/* Services Section */}
            <div className="lg:col-span-2 space-y-4">
          {/* Service Category Selection */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2">
                <Stethoscope className="h-5 w-5" />
                Add Services to Appointment
              </CardTitle>
              <p className="text-sm text-gray-600">
                Select a category and add services that will be performed during this appointment
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {serviceCategories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className="capitalize"
                  >
                    {category}
                  </Button>
                ))}
              </div>

              {selectedCategory && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium capitalize">{selectedCategory} Services</h4>
                    <Dialog open={showCreateServiceModal} onOpenChange={setShowCreateServiceModal}>
                      <DialogTrigger asChild>
                        <Button size="sm" variant="outline">
                          <Plus className="h-4 w-4 mr-1" />
                          Create New
                        </Button>
                      </DialogTrigger>
                      <DialogContent>
                        <DialogHeader>
                          <DialogTitle>Create New Service</DialogTitle>
                          <DialogDescription>
                            Create a new service for this clinic that can be used in appointments.
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-4">
                          <div>
                            <Label>Service Name</Label>
                            <Input
                              value={newServiceData.serviceName}
                              onChange={(e) => setNewServiceData({...newServiceData, serviceName: e.target.value})}
                              placeholder="Enter service name"
                            />
                          </div>
                          <div>
                            <Label>Category</Label>
                            <div className="p-2 bg-gray-50 rounded border">
                              <span className="text-sm font-medium capitalize">{selectedCategory}</span>
                              <p className="text-xs text-gray-600">Service will be created in this category</p>
                            </div>
                          </div>
                          <div>
                            <Label>Price (KES)</Label>
                            <Input
                              type="number"
                              value={newServiceData.defaultPrice}
                              onChange={(e) => setNewServiceData({...newServiceData, defaultPrice: Number(e.target.value)})}
                            />
                          </div>
                          <div>
                            <Label>Duration (minutes)</Label>
                            <Input
                              type="number"
                              value={newServiceData.estimatedDuration}
                              onChange={(e) => setNewServiceData({...newServiceData, estimatedDuration: Number(e.target.value)})}
                            />
                          </div>
                          <Button
                            onClick={handleCreateService}
                            disabled={createServiceMutation.isPending}
                            className="w-full"
                          >
                            {createServiceMutation.isPending ? 'Creating...' : 'Create Service'}
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {services
                      .filter((service: Service) => !selectedCategory || service.category === selectedCategory)
                      .map((service: Service) => (
                        <div
                          key={service.serviceId}
                          className="p-3 border rounded-lg hover:shadow-md cursor-pointer transition-all"
                          onClick={() => handleAddService(service)}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <h5 className="font-medium text-sm">{service.serviceName}</h5>
                            <Badge variant="outline" className="text-xs">
                              {service.currency} {service.defaultPrice}
                            </Badge>
                          </div>
                          {service.description && (
                            <p className="text-xs text-gray-600 mb-2">{service.description}</p>
                          )}
                          <div className="text-xs text-gray-500">
                            {service.estimatedDuration} min
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Appointment Services & Notes */}
        <div className="space-y-4">
          {/* Appointment Services */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">Appointment Services</CardTitle>
            </CardHeader>
            <CardContent>
              {selectedServices.length === 0 ? (
                <p className="text-gray-500 text-sm">No services added to this appointment</p>
              ) : (
                <div className="space-y-3">
                  {selectedServices.map((service) => (
                    <div key={service.serviceId} className="p-3 border rounded-lg bg-gray-50">
                      <div className="flex justify-between items-start mb-2">
                        <h6 className="font-medium text-sm">{service.serviceName}</h6>
                        <Badge variant="outline" className="text-xs">
                          {service.currency} {service.price}
                        </Badge>
                      </div>

                      {/* Staff Assignment */}
                      <div className="mb-2">
                        {service.assignedStaffName ? (
                          <Badge variant="default" className="text-xs bg-green-100 text-green-800">
                            <Users className="h-3 w-3 mr-1" />
                            Assigned to: {service.assignedStaffName}
                          </Badge>
                        ) : service.isExternalReferral ? (
                          <Badge variant="default" className="text-xs bg-blue-100 text-blue-800">
                            <ExternalLink className="h-3 w-3 mr-1" />
                            External: {service.externalClinic?.name}
                          </Badge>
                        ) : (
                          <div className="flex gap-1">
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-xs h-6"
                              onClick={() => {
                                setSelectedServiceForAssignment(service.serviceId);
                                setShowStaffAssignModal(true);
                              }}
                            >
                              <Users className="h-3 w-3 mr-1" />
                              Assign Staff
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-xs h-6"
                              onClick={() => {
                                setSelectedServiceForAssignment(service.serviceId);
                                setShowExternalReferralModal(true);
                              }}
                            >
                              <ExternalLink className="h-3 w-3 mr-1" />
                              External
                            </Button>
                          </div>
                        )}
                      </div>
                      <Textarea
                        placeholder="Add notes for this service..."
                        value={service.notes || ''}
                        onChange={(e) => handleUpdateServiceNotes(service.serviceId, e.target.value)}
                        className="text-xs"
                        rows={2}
                      />
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Enhanced Notes Section */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Appointment Notes by Category
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Category Selection Chips */}
              <div>
                <Label className="text-sm font-medium mb-2 block">Select Categories for Notes</Label>
                <div className="flex flex-wrap gap-2">
                  {serviceCategories.map((category) => {
                    const hasNotes = appointmentNotes.some(note => note.category === category);
                    const isSelected = selectedCategory === category;
                    return (
                      <Badge
                        key={category}
                        variant={isSelected ? "default" : hasNotes ? "secondary" : "outline"}
                        className={cn(
                          "cursor-pointer transition-all capitalize px-3 py-1",
                          isSelected && "bg-blue-600 text-white",
                          hasNotes && !isSelected && "bg-green-100 text-green-800",
                          !hasNotes && !isSelected && "hover:bg-gray-100"
                        )}
                        onClick={() => setSelectedCategory(category)}
                      >
                        {category}
                        {hasNotes && <span className="ml-1">✓</span>}
                      </Badge>
                    );
                  })}
                </div>
              </div>

              {/* AI Generation Options */}
              {selectedCategory && (
                <div className="space-y-3 p-3 bg-blue-50 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <h4 className="font-medium text-sm capitalize">AI Notes for {selectedCategory}</h4>
                    <Button
                      size="sm"
                      onClick={() => generateAINotes(selectedCategory)}
                      disabled={isGeneratingNotes}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      <Wand2 className="h-3 w-3 mr-1" />
                      {isGeneratingNotes ? 'Generating...' : 'Generate AI Notes'}
                    </Button>
                  </div>

                  {/* AI Generation Chips */}
                  <div className="space-y-2">
                    <Label className="text-xs">AI Generation Options:</Label>
                    <div className="flex flex-wrap gap-1">
                      {[
                        'Standard Assessment',
                        'Detailed Examination',
                        'Treatment Summary',
                        'Follow-up Instructions',
                        'Medication Notes',
                        'Behavioral Observations'
                      ].map((option) => (
                        <Badge
                          key={option}
                          variant="outline"
                          className="cursor-pointer text-xs hover:bg-blue-100"
                          onClick={() => generateAINotes(selectedCategory, option)}
                        >
                          {option}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Existing Notes Display */}
              {appointmentNotes.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium text-sm">Saved Notes</h4>
                  {appointmentNotes.map((note, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <Badge variant={note.isAIGenerated ? "default" : "secondary"} className="text-xs">
                          {note.category} {note.isAIGenerated && '(AI)'}
                        </Badge>
                        <div className="flex gap-1">
                          {note.isAIGenerated && (
                            <>
                              <Button size="sm" variant="outline" className="text-xs h-6">
                                Regenerate
                              </Button>
                              <Button size="sm" variant="outline" className="text-xs h-6">
                                Accept
                              </Button>
                            </>
                          )}
                          <Button size="sm" variant="ghost" className="text-xs h-6 text-red-600">
                            Delete
                          </Button>
                        </div>
                      </div>
                      <p className="text-sm whitespace-pre-wrap">{note.content}</p>
                    </div>
                  ))}
                </div>
              )}

              {/* Manual Note Addition */}
              {selectedCategory && (
                <div className="space-y-2">
                  <Label className="text-sm">Add Manual Note for {selectedCategory}</Label>
                  <Textarea
                    placeholder={`Write notes for ${selectedCategory}...`}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && e.ctrlKey) {
                        const content = e.currentTarget.value;
                        if (content.trim()) {
                          handleAddManualNote(selectedCategory, content);
                          e.currentTarget.value = '';
                        }
                      }
                    }}
                    className="text-sm"
                    rows={3}
                  />
                  <p className="text-xs text-gray-500">Press Ctrl+Enter to add note</p>
                </div>
              )}
            </CardContent>
          </Card>
            </div>
          </div>
        </TabsContent>

        {/* AI Suggestions Tab */}
        <TabsContent value="ai-suggestions" className="mt-6">
          <AISuggestions
            appointmentId={parseInt(id!)}
            petData={appointment?.pet}
            appointmentData={appointment}
            clinicId={appointment?.clinicId}
          />
        </TabsContent>

        {/* Completion Tab */}
        <TabsContent value="completion" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Complete Appointment
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="text-center py-8">
                <div className="space-y-4">
                  <div className="text-lg font-semibold">Appointment Summary</div>
                  <div className="text-sm text-gray-600">
                    Review all completed tasks and generate final summary
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                    <Button
                      onClick={() => navigate(`/appointments/${id}/task-status`)}
                      variant="outline"
                      className="w-full"
                    >
                      View Task Status
                    </Button>
                    <Button
                      onClick={() => navigate(`/invoices/appointment/${id}`)}
                      className="w-full bg-green-600 hover:bg-green-700"
                    >
                      Generate Invoice
                    </Button>
                    <Button
                      onClick={() => navigate(`/receipts/appointment/${id}`)}
                      variant="outline"
                      className="w-full"
                    >
                      Generate Receipt
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Staff Assignment Modal */}
      <Dialog open={showStaffAssignModal} onOpenChange={setShowStaffAssignModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Assign Staff Member</DialogTitle>
            <DialogDescription>
              Select a staff member to assign this service to.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            {clinicStaff.length === 0 ? (
              <p className="text-gray-500">No staff members available</p>
            ) : (
              <div className="grid grid-cols-1 gap-3 max-h-96 overflow-y-auto">
                {clinicStaff.map((staff: any) => (
                  <div
                    key={staff.staffId}
                    className="p-3 border rounded-lg hover:shadow-md cursor-pointer transition-all"
                    onClick={() => handleAssignStaff(
                      selectedServiceForAssignment!,
                      staff.staffId,
                      `${staff.firstName} ${staff.lastName}`
                    )}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <h6 className="font-medium">{staff.firstName} {staff.lastName}</h6>
                        <p className="text-sm text-gray-600">{staff.jobTitle}</p>
                        <p className="text-xs text-gray-500">{staff.email}</p>
                      </div>
                      <Badge variant="outline">{staff.jobTitle}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* External Referral Modal */}
      <Dialog open={showExternalReferralModal} onOpenChange={setShowExternalReferralModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>External Clinic Referral</DialogTitle>
            <DialogDescription>
              Refer this service to an external clinic or specialist.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label>Clinic/Specialist Name</Label>
              <Input
                value={externalClinicData.name}
                onChange={(e) => setExternalClinicData({...externalClinicData, name: e.target.value})}
                placeholder="Enter clinic or specialist name"
              />
            </div>
            <div>
              <Label>Contact Information</Label>
              <Input
                value={externalClinicData.contact}
                onChange={(e) => setExternalClinicData({...externalClinicData, contact: e.target.value})}
                placeholder="Phone number or email"
              />
            </div>
            <div>
              <Label>Address (Optional)</Label>
              <Input
                value={externalClinicData.address}
                onChange={(e) => setExternalClinicData({...externalClinicData, address: e.target.value})}
                placeholder="Clinic address"
              />
            </div>
            <Button
              onClick={() => handleExternalReferral(selectedServiceForAssignment!)}
              className="w-full"
            >
              Create Referral
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Completion Options Modal */}
      <Dialog open={showCompletionOptions} onOpenChange={setShowCompletionOptions}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Appointment Completed</DialogTitle>
            <DialogDescription>
              All categories have been documented. Choose your next action.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              <Button
                onClick={() => navigate(`/invoices/appointment/${id}`)}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                Generate Invoice
              </Button>
              <Button
                onClick={() => navigate(`/appointments/${id}`)}
                variant="outline"
                className="w-full"
              >
                View Appointment Details
              </Button>
              <Button
                onClick={() => navigate(`/receipts/appointment/${id}`)}
                variant="outline"
                className="w-full"
              >
                Generate Receipt
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default StartAppointment;
