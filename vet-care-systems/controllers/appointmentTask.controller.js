import Appointment from '../models/appointment.model.js';
import Staff from '../models/staff.model.js';
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Assign categories to staff for an appointment
 */
export const assignCategoriesToStaff = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { selectedCategories, vetInCharge, clinicId } = req.body;

        if (!selectedCategories || !Array.isArray(selectedCategories)) {
            return sendResponse(res, 400, false, "Selected categories array is required");
        }

        // Get appointment
        const appointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        });

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Validate clinicId if provided
        if (clinicId && appointment.clinicId !== clinicId) {
            return sendResponse(res, 403, false, "Access denied: Appointment belongs to different clinic");
        }

        // Validate staff assignments
        for (const categoryAssignment of selectedCategories) {
            const staff = await Staff.findOne({
                staffId: categoryAssignment.assignedStaff,
                status: 1
            });

            if (!staff) {
                return sendResponse(res, 400, false, `Staff member ${categoryAssignment.assignedStaff} not found or inactive`);
            }
        }

        // Validate vet in charge if provided
        if (vetInCharge) {
            const vet = await Staff.findOne({
                staffId: vetInCharge,
                status: 1
            });

            if (!vet) {
                return sendResponse(res, 400, false, "Veterinarian in charge not found or inactive");
            }
        }

        // Update appointment with category assignments
        const updatedAppointment = await Appointment.findOneAndUpdate(
            { appointmentId: parseInt(appointmentId) },
            {
                selectedCategories: selectedCategories.map(cat => ({
                    ...cat,
                    isCompleted: false,
                    priority: cat.priority || 'normal',
                    estimatedDuration: cat.estimatedDuration || 30
                })),
                vetInCharge: vetInCharge || appointment.staffId,
                completionStatus: 'in_progress',
                updatedBy: req.user?.staffId || req.user?.userId || 1001
            },
            { new: true, runValidators: true }
        ).populate('vetInCharge', 'staffId firstName lastName jobTitle')
        .lean();

        return sendResponse(res, 200, true, "Categories assigned successfully", {
            appointment: updatedAppointment,
            assignedCategories: updatedAppointment.selectedCategories
        });

    } catch (error) {
        console.error("Assign categories error:", error);
        return sendResponse(res, 500, false, `Failed to assign categories: ${error.message}`);
    }
};

/**
 * Complete a category task
 */
export const completeCategoryTask = async (req, res) => {
    try {
        const { appointmentId, category } = req.params;
        const { signature, actualDuration, notes } = req.body;

        const appointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        });

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find the category in selectedCategories
        const categoryIndex = appointment.selectedCategories.findIndex(
            cat => cat.category === category
        );

        if (categoryIndex === -1) {
            return sendResponse(res, 404, false, "Category not found in appointment");
        }

        // Check if the current user is assigned to this category
        const categoryTask = appointment.selectedCategories[categoryIndex];
        const currentUserId = req.user?.staffId || req.user?.userId || 1001;

        if (categoryTask.assignedStaff !== currentUserId) {
            return sendResponse(res, 403, false, "You are not assigned to this category");
        }

        // Update the category as completed
        appointment.selectedCategories[categoryIndex] = {
            ...categoryTask,
            isCompleted: true,
            completedAt: new Date(),
            completedBy: currentUserId,
            signature: signature || `Completed by ${currentUserId}`,
            actualDuration: actualDuration || categoryTask.estimatedDuration
        };

        // Check if all categories are completed
        const allCompleted = appointment.selectedCategories.every(cat => cat.isCompleted);
        if (allCompleted) {
            appointment.completionStatus = 'completed';
            appointment.status = 'completed';
        }

        await appointment.save();

        // Get updated appointment with populated data
        const updatedAppointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        }).populate('vetInCharge', 'staffId firstName lastName jobTitle')
        .lean();

        return sendResponse(res, 200, true, "Category task completed successfully", {
            appointment: updatedAppointment,
            completedCategory: category,
            allTasksCompleted: allCompleted
        });

    } catch (error) {
        console.error("Complete category task error:", error);
        return sendResponse(res, 500, false, `Failed to complete category task: ${error.message}`);
    }
};

/**
 * Get appointment task status
 */
export const getAppointmentTaskStatus = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        const appointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        }).populate('vetInCharge', 'staffId firstName lastName jobTitle')
        .populate('selectedCategories.assignedStaff', 'staffId firstName lastName jobTitle')
        .populate('selectedCategories.completedBy', 'staffId firstName lastName')
        .lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Calculate completion statistics
        const totalTasks = appointment.selectedCategories?.length || 0;
        const completedTasks = appointment.selectedCategories?.filter(cat => cat.isCompleted).length || 0;
        const completionPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

        // Calculate total estimated and actual duration
        const totalEstimatedDuration = appointment.selectedCategories?.reduce(
            (sum, cat) => sum + (cat.estimatedDuration || 0), 0
        ) || 0;

        const totalActualDuration = appointment.selectedCategories?.reduce(
            (sum, cat) => sum + (cat.actualDuration || 0), 0
        ) || 0;

        return sendResponse(res, 200, true, "Appointment task status retrieved successfully", {
            appointment,
            taskSummary: {
                totalTasks,
                completedTasks,
                pendingTasks: totalTasks - completedTasks,
                completionPercentage,
                totalEstimatedDuration,
                totalActualDuration,
                isFullyCompleted: completedTasks === totalTasks && totalTasks > 0
            }
        });

    } catch (error) {
        console.error("Get appointment task status error:", error);
        return sendResponse(res, 500, false, `Failed to get appointment task status: ${error.message}`);
    }
};

/**
 * Reassign category to different staff
 */
export const reassignCategory = async (req, res) => {
    try {
        const { appointmentId, category } = req.params;
        const { newStaffId, reason } = req.body;

        if (!newStaffId) {
            return sendResponse(res, 400, false, "New staff ID is required");
        }

        // Validate new staff
        const newStaff = await Staff.findOne({
            staffId: parseInt(newStaffId),
            status: 1
        });

        if (!newStaff) {
            return sendResponse(res, 404, false, "New staff member not found or inactive");
        }

        const appointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        });

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Find and update the category
        const categoryIndex = appointment.selectedCategories.findIndex(
            cat => cat.category === category
        );

        if (categoryIndex === -1) {
            return sendResponse(res, 404, false, "Category not found in appointment");
        }

        // Check if category is already completed
        if (appointment.selectedCategories[categoryIndex].isCompleted) {
            return sendResponse(res, 400, false, "Cannot reassign completed category");
        }

        // Update assignment
        appointment.selectedCategories[categoryIndex].assignedStaff = parseInt(newStaffId);
        await appointment.save();

        // Get updated appointment
        const updatedAppointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        }).populate('selectedCategories.assignedStaff', 'staffId firstName lastName jobTitle')
        .lean();

        return sendResponse(res, 200, true, "Category reassigned successfully", {
            appointment: updatedAppointment,
            reassignedCategory: category,
            newStaff: `${newStaff.firstName} ${newStaff.lastName}`,
            reason: reason || 'No reason provided'
        });

    } catch (error) {
        console.error("Reassign category error:", error);
        return sendResponse(res, 500, false, `Failed to reassign category: ${error.message}`);
    }
};

/**
 * Generate appointment summary from completed tasks
 */
export const generateAppointmentSummary = async (req, res) => {
    try {
        const { appointmentId } = req.params;

        const appointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        }).populate('selectedCategories.completedBy', 'staffId firstName lastName')
        .lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Check if all tasks are completed
        const allCompleted = appointment.selectedCategories?.every(cat => cat.isCompleted) || false;

        if (!allCompleted) {
            return sendResponse(res, 400, false, "Cannot generate summary until all tasks are completed");
        }

        // Generate AI summary from service notes and completed tasks
        const summaryContent = generateSummaryContent(appointment);

        // Update appointment with AI summary
        const updatedAppointment = await Appointment.findOneAndUpdate(
            { appointmentId: parseInt(appointmentId) },
            {
                aiSummary: {
                    content: summaryContent,
                    generatedAt: new Date(),
                    generatedBy: req.user?.staffId || req.user?.userId || 1001,
                    isApproved: false
                }
            },
            { new: true }
        ).lean();

        return sendResponse(res, 200, true, "Appointment summary generated successfully", {
            appointment: updatedAppointment,
            summary: summaryContent
        });

    } catch (error) {
        console.error("Generate appointment summary error:", error);
        return sendResponse(res, 500, false, `Failed to generate appointment summary: ${error.message}`);
    }
};

/**
 * Helper function to generate summary content
 */
function generateSummaryContent(appointment) {
    const completedCategories = appointment.selectedCategories?.filter(cat => cat.isCompleted) || [];

    let summary = `Appointment Summary for Appointment #${appointment.appointmentId}\n\n`;
    summary += `Date: ${new Date(appointment.dateTime).toLocaleDateString()}\n`;
    summary += `Duration: ${appointment.duration} minutes\n\n`;

    summary += `Completed Tasks:\n`;
    completedCategories.forEach(cat => {
        summary += `- ${cat.category.charAt(0).toUpperCase() + cat.category.slice(1)}: `;
        summary += `Completed by Staff ID ${cat.completedBy} `;
        summary += `(Duration: ${cat.actualDuration || cat.estimatedDuration} minutes)\n`;
    });

    summary += `\nOverall Status: All tasks completed successfully\n`;
    summary += `Total Actual Duration: ${completedCategories.reduce((sum, cat) => sum + (cat.actualDuration || cat.estimatedDuration || 0), 0)} minutes\n`;

    if (appointment.appointmentNotes?.length > 0) {
        summary += `\nAdditional Notes:\n`;
        appointment.appointmentNotes.forEach(note => {
            summary += `- ${note.category}: ${note.content}\n`;
        });
    }

    return summary;
}
