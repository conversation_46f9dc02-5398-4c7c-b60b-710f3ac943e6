import AISuggestion from '../models/aiSuggestion.model.js';
import Appointment from '../models/appointment.model.js';
import Pet from '../models/pet.model.js';
import { sendResponse } from '../utils/responseHandler.js';

/**
 * Generate AI suggestions for appointment
 */
export const generateAISuggestions = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const {
            symptoms = [],
            vitalSigns = {},
            previousConditions = [],
            currentMedications = [],
            allergies = [],
            requestedCategories = ['diagnosis', 'treatment', 'medication']
        } = req.body;

        // Get appointment details
        const appointment = await Appointment.findOne({
            appointmentId: parseInt(appointmentId)
        }).lean();

        if (!appointment) {
            return sendResponse(res, 404, false, "Appointment not found");
        }

        // Get pet details
        const pet = await Pet.findOne({
            petId: appointment.petId
        }).lean();

        if (!pet) {
            return sendResponse(res, 404, false, "Pet not found");
        }

        // Generate suggestions for each requested category
        const suggestions = [];

        for (const category of requestedCategories) {
            const suggestion = await generateCategorySuggestion(
                appointment,
                pet,
                category,
                {
                    symptoms,
                    vitalSigns,
                    previousConditions,
                    currentMedications,
                    allergies
                },
                req.user?.staffId || req.user?.userId || 1001
            );

            if (suggestion) {
                suggestions.push(suggestion);
            }
        }

        // Save suggestions to database
        const savedSuggestions = await AISuggestion.insertMany(suggestions);

        return sendResponse(res, 201, true, "AI suggestions generated successfully", {
            suggestions: savedSuggestions,
            count: savedSuggestions.length
        });

    } catch (error) {
        console.error("Generate AI suggestions error:", error);
        return sendResponse(res, 500, false, `Failed to generate AI suggestions: ${error.message}`);
    }
};

/**
 * Get AI suggestions for appointment
 */
export const getAppointmentSuggestions = async (req, res) => {
    try {
        const { appointmentId } = req.params;
        const { category, status, priority } = req.query;

        const filter = { appointmentId: parseInt(appointmentId) };

        if (category) filter.category = category;
        if (status) filter.status = status;
        if (priority) filter.priority = priority;

        const suggestions = await AISuggestion.find(filter)
            .populate('generatedByStaff', 'staffId firstName lastName')
            .populate('reviewedByStaff', 'staffId firstName lastName')
            .sort({ createdAt: -1 })
            .lean();

        return sendResponse(res, 200, true, "AI suggestions retrieved successfully", {
            suggestions,
            count: suggestions.length
        });

    } catch (error) {
        console.error("Get AI suggestions error:", error);
        return sendResponse(res, 500, false, `Failed to get AI suggestions: ${error.message}`);
    }
};

/**
 * Review AI suggestion
 */
export const reviewSuggestion = async (req, res) => {
    try {
        const { suggestionId } = req.params;
        const { status, reviewNotes } = req.body;

        if (!['accepted', 'rejected'].includes(status)) {
            return sendResponse(res, 400, false, "Status must be 'accepted' or 'rejected'");
        }

        const suggestion = await AISuggestion.findOneAndUpdate(
            { suggestionId: parseInt(suggestionId) },
            {
                status,
                reviewNotes,
                reviewedBy: req.user?.staffId || req.user?.userId || 1001,
                reviewedAt: new Date()
            },
            { new: true, runValidators: true }
        ).lean();

        if (!suggestion) {
            return sendResponse(res, 404, false, "AI suggestion not found");
        }

        return sendResponse(res, 200, true, "AI suggestion reviewed successfully", {
            suggestion
        });

    } catch (error) {
        console.error("Review AI suggestion error:", error);
        return sendResponse(res, 500, false, `Failed to review AI suggestion: ${error.message}`);
    }
};

/**
 * Implement AI suggestion
 */
export const implementSuggestion = async (req, res) => {
    try {
        const { suggestionId } = req.params;
        const { implementationNotes } = req.body;

        const suggestion = await AISuggestion.findOneAndUpdate(
            { suggestionId: parseInt(suggestionId) },
            {
                status: 'implemented',
                implementedBy: req.user?.staffId || req.user?.userId || 1001,
                implementedAt: new Date(),
                implementationNotes
            },
            { new: true, runValidators: true }
        ).lean();

        if (!suggestion) {
            return sendResponse(res, 404, false, "AI suggestion not found");
        }

        return sendResponse(res, 200, true, "AI suggestion implemented successfully", {
            suggestion
        });

    } catch (error) {
        console.error("Implement AI suggestion error:", error);
        return sendResponse(res, 500, false, `Failed to implement AI suggestion: ${error.message}`);
    }
};

/**
 * Record suggestion outcome
 */
export const recordOutcome = async (req, res) => {
    try {
        const { suggestionId } = req.params;
        const { outcome } = req.body;

        const suggestion = await AISuggestion.findOneAndUpdate(
            { suggestionId: parseInt(suggestionId) },
            { outcome },
            { new: true, runValidators: true }
        ).lean();

        if (!suggestion) {
            return sendResponse(res, 404, false, "AI suggestion not found");
        }

        return sendResponse(res, 200, true, "Suggestion outcome recorded successfully", {
            suggestion
        });

    } catch (error) {
        console.error("Record suggestion outcome error:", error);
        return sendResponse(res, 500, false, `Failed to record suggestion outcome: ${error.message}`);
    }
};

/**
 * Helper function to generate category-specific suggestions
 */
async function generateCategorySuggestion(appointment, pet, category, context, staffId) {
    // This is a mock implementation - in production, integrate with actual AI APIs
    const suggestionTemplates = {
        diagnosis: {
            title: "Diagnostic Assessment",
            description: "Based on the presented symptoms and examination findings, consider the following diagnostic possibilities.",
            suggestionType: "diagnostic_test",
            diagnosticTests: [
                {
                    testName: "Complete Blood Count (CBC)",
                    reason: "To assess overall health and detect infections or blood disorders",
                    urgency: "routine",
                    estimatedCost: 150
                },
                {
                    testName: "Basic Metabolic Panel",
                    reason: "To evaluate organ function and electrolyte balance",
                    urgency: "routine",
                    estimatedCost: 200
                }
            ]
        },
        treatment: {
            title: "Treatment Recommendations",
            description: "Suggested treatment plan based on current assessment and pet's condition.",
            suggestionType: "treatment_plan",
            treatments: [
                {
                    name: "Supportive Care",
                    description: "Monitor vital signs and provide comfort measures",
                    estimatedDuration: 30,
                    estimatedCost: 100,
                    currency: "KES"
                }
            ]
        },
        medication: {
            title: "Medication Recommendations",
            description: "Suggested medications based on the pet's condition and medical history.",
            suggestionType: "medication_recommendation",
            medications: [
                {
                    name: "Pain Relief",
                    dosage: "As prescribed",
                    frequency: "As needed",
                    duration: "3-5 days",
                    instructions: "Give with food to reduce stomach irritation"
                }
            ]
        }
    };

    const template = suggestionTemplates[category];
    if (!template) return null;

    return {
        appointmentId: appointment.appointmentId,
        petId: appointment.petId,
        category,
        suggestionType: template.suggestionType,
        title: template.title,
        description: template.description,
        confidenceScore: Math.floor(Math.random() * 30) + 70, // 70-100
        priority: 'medium',
        medications: template.medications || [],
        treatments: template.treatments || [],
        diagnosticTests: template.diagnosticTests || [],
        followUp: {
            recommended: true,
            timeframe: "1-2 weeks",
            reason: "Monitor progress and adjust treatment as needed"
        },
        aiModel: {
            provider: 'openai',
            model: 'gpt-4',
            version: '1.0',
            temperature: 0.7,
            maxTokens: 1000
        },
        context,
        generatedBy: staffId,
        status: 'pending'
    };
}
